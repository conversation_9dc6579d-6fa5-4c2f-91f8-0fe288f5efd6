import { supabase, db, Task } from '@/lib/supabase';
import { KanbanColumn, CalendarEvent, GanttTask } from '@/lib/types/task';

export const taskService = {
  // Create a new task
  async createTask(task: Omit<Task, 'id' | 'created_at' | 'updated_at'>) {
    try {
      const result = await db.createTask(task);
      if (!result) {
        throw new Error('Failed to create task');
      }
      return result;
    } catch (error) {
      console.error('Error creating task:', error);
      throw error;
    }
  },

  // Update an existing task
  async updateTask(taskId: string, updates: Partial<Task>) {
    try {
      const result = await db.updateTask(taskId, updates);
      if (!result) {
        throw new Error('Failed to update task');
      }
      return result;
    } catch (error) {
      console.error('Error updating task:', error);
      throw error;
    }
  },

  // Delete a task
  async deleteTask(taskId: string) {
    try {
      const success = await db.deleteTask(taskId);
      if (!success) {
        throw new Error('Failed to delete task');
      }
    } catch (error) {
      console.error('Error deleting task:', error);
      throw error;
    }
  },

  // Get tasks for a specific user
  async getUserTasks(userId: string): Promise<Task[]> {
    try {
      return await db.getTasks(userId);
    } catch (error) {
      console.error('Error getting user tasks:', error);
      throw error;
    }
  },

  // Get tasks for a specific user (alias)
  async getTasks(userId: string): Promise<Task[]> {
    try {
      return await this.getUserTasks(userId);
    } catch (error) {
      console.error('Error getting tasks:', error);
      throw error;
    }
  },

  // Get tasks for Kanban view
  async getKanbanTasks(userId: string): Promise<KanbanColumn[]> {
    try {
      const tasks = await this.getUserTasks(userId);
      const columns: KanbanColumn[] = [
        { id: 'todo', title: 'To Do', tasks: [] },
        { id: 'in-progress', title: 'In Progress', tasks: [] },
        { id: 'done', title: 'Done', tasks: [] }
      ];
      
      tasks.forEach(task => {
        const column = columns.find(col => col.id === task.status);
        if (column) {
          column.tasks.push(task);
        }
      });
      
      return columns;
    } catch (error) {
      console.error('Error getting Kanban tasks:', error);
      throw error;
    }
  },

  // Get tasks for Calendar view
  async getCalendarEvents(userId: string): Promise<CalendarEvent[]> {
    try {
      const tasks = await this.getUserTasks(userId);
      return tasks.map(task => ({
        ...task,
        allDay: false
      }));
    } catch (error) {
      console.error('Error getting calendar events:', error);
      throw error;
    }
  },

  // Get tasks for Gantt view
  async getGanttTasks(userId: string): Promise<GanttTask[]> {
    try {
      const tasks = await this.getUserTasks(userId);
      return tasks.map(task => ({
        ...task,
        dependencies: [],
        progress: 0,
        duration: 1
      }));
    } catch (error) {
      console.error('Error getting Gantt tasks:', error);
      throw error;
    }
  }
};